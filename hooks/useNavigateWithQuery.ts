'use client'

import { useRouter } from 'next/navigation'

type QueryParams = Record<string, string | number | boolean | undefined | null>

export function useNavigateWithQuery() {
  const router = useRouter()

  const navigateWithQuery = (pathname: string, query: QueryParams = {}) => {
    const url = new URL(pathname, window.location.origin)

    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.set(key, String(value))
      }
    })

    router.push(url.toString())
  }

  return navigateWithQuery
}
