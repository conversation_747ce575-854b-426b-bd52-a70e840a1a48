'use client'

import { useRouter, useSearchParams } from 'next/navigation'

import { HomeTabKey } from '@/app/[locale]/(commonLayout)/(home)/compoents/PumpList'
import { ITabs } from '@/app/[locale]/(commonLayout)/(user)/user/[address]/page'
import { ITabKey } from '@/app/[locale]/(commonLayout)/explore/page'
import { IGetPumpTokensReq, TSortTypeKey } from '@/types/IPump'
import { DisplayMode } from '@/types/nft'

interface ISearchParams {
  displayMode?: DisplayMode
  type?: ITabKey | HomeTabKey | ITabs
  // searchUser?: string
  // searchNft?: string
}
// export const defaultExplore = {
//   sortType: undefined,
//   filterType: undefined,
//   timeFrame: undefined,
//   search: undefined,
//   chainId: undefined,
//   creatorAddress: undefined,
//   userAddress: undefined,
//   onlyHolding: undefined,
//   onlyFavorite: undefined,
//   selectedDropdown: undefined,
//   displayMode: undefined,
//   searchUser: undefined,
//   searchNft: undefined,
// }
export function useUpdateSearchParams(tabPrefix?: string) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const updateSearchParams = (updates: IGetPumpTokensReq & ISearchParams) => {
    const params = new URLSearchParams(searchParams)

    Object.entries(updates).forEach(([key, value]) => {
      const paramKey = tabPrefix && isFilterParam(key) ? `${tabPrefix}_${key}` : key

      if (value === null || value === undefined) {
        params.delete(paramKey)
      } else {
        params.set(paramKey, String(value))
      }
    })
    console.log(params.toString())

    router.replace(`?${params.toString()}`)
  }

  return updateSearchParams
}

function isFilterParam(key: string): boolean {
  const filterParams = [
    'search',
    'filterType',
    'timeFrame',
    'sortType',
    'chainId',
    'onlyFavorite',
    'userAddress',
    'selectedDropdown',
    'displayMode',
  ]
  return filterParams.includes(key)
}

export function useReadSearchParams(tabPrefix?: string) {
  const searchParams = useSearchParams()

  const readSearchParams = (): Partial<IGetPumpTokensReq> & { selectedDropdown?: TSortTypeKey } & ISearchParams => {
    const params: Partial<IGetPumpTokensReq> & { selectedDropdown?: TSortTypeKey } & ISearchParams = {}

    const getParam = (key: string) => {
      if (tabPrefix && isFilterParam(key)) {
        return searchParams.get(`${tabPrefix}_${key}`)
      }
      return searchParams.get(key)
    }

    const search = getParam('search')
    if (search) params.search = search

    const filterType = getParam('filterType')
    if (filterType) params.filterType = filterType as 'ALL' | 'FINALIZED'

    const timeFrame = getParam('timeFrame')
    if (timeFrame) params.timeFrame = timeFrame as '5MIN' | '1H' | '6H' | '1D'

    const sortType = getParam('sortType')
    if (sortType) params.sortType = sortType as 'NEWEST'

    const chainId = getParam('chainId')
    if (chainId && chainId !== 'all') {
      const numChainId = Number(chainId)
      params.chainId = isNaN(numChainId) ? chainId : numChainId
    }

    const onlyFavorite = getParam('onlyFavorite')
    if (onlyFavorite === 'true') params.onlyFavorite = true

    const userAddress = getParam('userAddress')
    if (userAddress) params.userAddress = userAddress as any

    if (params.sortType === 'NEWEST') {
      params.selectedDropdown = 'newest'
    } else if (params.timeFrame) {
      const timeFrameToDropdownMap: Record<string, TSortTypeKey> = {
        '5MIN': 'fiveminutes',
        '1H': 'onehour',
        '6H': 'sixhours',
        '1D': 'oneday',
      }
      params.selectedDropdown = timeFrameToDropdownMap[params.timeFrame]
    }

    const displayMode = getParam('displayMode')
    if (displayMode) params.displayMode = displayMode as DisplayMode

    return params
  }

  return readSearchParams
}

export function useClearTabFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const clearTabFilters = (tabPrefix: string) => {
    const params = new URLSearchParams(searchParams)

    const filterParams = [
      'search',
      'filterType',
      'timeFrame',
      'sortType',
      'chainId',
      'onlyFavorite',
      'userAddress',
      'selectedDropdown',
      'displayMode',
    ]

    filterParams.forEach((param) => {
      params.delete(`${tabPrefix}_${param}`)
    })

    router.replace(`?${params.toString()}`)
  }

  return clearTabFilters
}
