'use client'

import { useRouter, useSearchParams } from 'next/navigation'

import { IGetPumpTokensReq } from '@/types/IPump'

export function useUpdateSearchParams() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const updateSearchParams = (updates: IGetPumpTokensReq) => {
    const params = new URLSearchParams(searchParams.toString())

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        params.delete(key)
      } else {
        params.set(key, String(value))
      }
    })

    router.replace(`?${params.toString()}`)
  }

  return updateSearchParams
}
