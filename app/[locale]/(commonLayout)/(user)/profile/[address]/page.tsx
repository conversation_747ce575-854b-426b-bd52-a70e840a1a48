'use client'

import React, { useEffect, useMemo, useState } from 'react'

import { Spacer } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import dayjs from 'dayjs'
import { useSearchParams } from 'next/navigation'
import { twMerge } from 'tailwind-merge'
import { useMediaQuery } from 'usehooks-ts'

import Activity from '@/components/Activity'
import BackComp from '@/components/BackComp'
import Tab from '@/components/library/Tab'
import TabWithMore from '@/components/library/Tab/TabWithMore'

import useScrollPosition from '../../../pump404/hooks/useScrollPosition'
// import userState from '@/store/user-store'
import Collectons from './components/Collectons'
import Created from './components/Created'
import { SearchOwnerNfts } from './components/graphql'
import Nfts from './components/Nfts'
import OnSale from './components/OnSale'
import UserCover from './components/UserCover'
import { UserGraphContext } from './components/UserGraphContext'
import useGetOwnerLaunch from './hook/useGetOwnerLaunch'
import useGraphNftList from './hook/useGraphNftList'
import PumpNfts from '@/app/[locale]/(commonLayout)/pump404/components/Nfts'
import PumpTokens from '@/app/[locale]/(commonLayout)/pump404/components/tokens'
// import useNftList from './hook/useNftList'
// import useSaleNftList from './hook/useSaleNftList'
// import { UserNftQueryDocument } from '@/graphql/generated'
import useElectionAddress from '@/hooks/contract/address/useElectionAddress'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import { isEVMAddress } from '@/hooks/useSafeAddress'
import useTheme from '@/hooks/useTheme'
import { useClearTabFilters, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
// import useAlgChain from '@/hooks/useAlgChain'
import { supportChainId, useAppKitAccount } from '@/libs/contract/wagmiConf'
import { user_graph } from '@/libs/graph'
import { toLowerCaseLettersOnly } from '@/libs/utils/util'
// import { getPumpTokens } from '@/request/api/pump'
import { IAddress } from '@/types/IAddress'

type ITabs = 'createPump' | 'nfts' | 'onSale' | 'collections' | 'activity' | 'tokens'
const Profile = () => {
  const searchParams = useSearchParams()
  const type = (searchParams.get('type') || 'createPump') as ITabs
  const [currentTab, setCurrentTab] = useState<ITabs>(type)
  const updateSearchParams = useUpdateSearchParams()
  const clearTabFilters = useClearTabFilters() // const algChain = useAlgChain()
  const { t } = useTranslate('profile')
  const { isDark } = useTheme()
  const { elementRef, atLeft, atRight } = useScrollPosition()
  const { address } = useAppKitAccount()
  const fusion = useFusionAddress()
  const election = useElectionAddress()
  // const isSignIn = userState((state) => state.isSignIn)
  // const [pumpTotal, setPumpTotal] = useState(0)
  const [allPumpContracts, setAllPumpContracts] = useState<string[]>([])
  const [pumpContracts, setPumpContracts] = useState<string[]>([])
  const [ownerContracts, setOwnerContracts] = useState<string[]>([])
  const isPhone = useMediaQuery('(max-width: 768px)')
  // useEffect(() => {
  //   const getTokens = async () => {
  //     try {
  //       const {
  //         data: { total },
  //       } = await getPumpTokens({
  //         creatorAddress: address,
  //         filterType: 'ALL',
  //         sortType: 'TOP_GAINERS',
  //         timeFrame: '7D',
  //         chainId: algChain,
  //         page: 1,
  //         pageSize: 1,
  //       })
  //       setPumpTotal(total)
  //     } catch (error) {
  //       setPumpTotal(0)
  //     }
  //   }
  //   if (address) {
  //     getTokens()
  //   }
  // }, [address])
  const {
    data: ownerNfts,
    refetch: allReftch,
    resetCache: allCahche,
  } = useGraphNftList(
    address as IAddress,
    {
      // chainId: supportChainId,
      contractAddress_in: ownerContracts,
      ownerId: toLowerCaseLettersOnly(address as IAddress) as IAddress,
      limit: 1000,
      orderBy: 'level',
      orderDirection: 'desc',
    },
    SearchOwnerNfts
  )
  const {
    data: owenerSale,
    refetch: saleReftch,
    resetCache: saleCache,
  } = useGraphNftList(
    address as IAddress,
    {
      chainId: supportChainId,
      contractAddress_in: ownerContracts,
      ownerId: toLowerCaseLettersOnly(address as IAddress) as IAddress,
      limit: 1000,
      orderBy: 'level',
      orderDirection: 'desc',
      price_not: 0,
      priceEndTime_gt: dayjs().unix(),
    },
    SearchOwnerNfts
  )
  const { data: allPumpCollections, isSuccess: isAllPumpSuccess } = useGetOwnerLaunch(address, 'all')
  const { data: pumpCollections, isSuccess: isPumpSuccess } = useGetOwnerLaunch(address, 'owner')
  const { data: pumpTokens } = useGetOwnerLaunch(address, 'token')
  const tabs = useMemo(
    () => [
      {
        key: 'createPump',
        title: t('Created'),
        badge: pumpCollections?.data && pumpCollections?.data?.total > 0 ? pumpCollections?.data?.total : '',
      },
      {
        key: 'tokens',
        title: t('Tokens'),
        badge: pumpTokens?.data && pumpTokens?.data?.total > 0 ? pumpTokens?.data?.total : '',
      },
      {
        key: 'nfts',
        title: t('profile_nfts'),
        badge:
          ownerNfts?.data?.nfts?.totalCount && ownerNfts?.data?.nfts?.totalCount > 0
            ? ownerNfts?.data?.nfts?.totalCount
            : '',
      },
      {
        key: 'onSale',
        title: t('onSale'),
        badge:
          owenerSale?.data?.nfts?.totalCount && owenerSale?.data?.nfts?.totalCount > 0
            ? owenerSale?.data?.nfts?.totalCount
            : '',
      },
      {
        key: 'activity',
        title: t('Activity'),
      },
    ],
    [owenerSale?.data?.nfts?.totalCount, ownerNfts?.data?.nfts?.totalCount, pumpCollections?.data, t]
  )
  useEffect(() => {
    if (isPumpSuccess) {
      const arr: any[] = []
      pumpCollections?.data?.items?.map((item: { contractAddress: any }) => {
        arr.push(item?.contractAddress?.toLocaleLowerCase())
      })
      setPumpContracts([...arr])
    }
    // console.log('pumpCollections', pumpCollections)
  }, [pumpCollections, isPumpSuccess, fusion, election])
  // useEffect(() => {
  //   if (isSignIn) {
  //     tokenRefetch()
  //   }
  // }, [isSignIn])
  useEffect(() => {
    if (isAllPumpSuccess) {
      const arr: any[] = []
      allPumpCollections?.data?.items?.map((item: { contractAddress: any }) => {
        isEVMAddress(item?.contractAddress)
          ? arr.push(item?.contractAddress?.toLocaleLowerCase())
          : arr.push(item?.contractAddress?.toString())
      })
      setAllPumpContracts([...arr])
      setOwnerContracts([...arr, fusion?.toLocaleLowerCase(), election?.toLocaleLowerCase()])
    }
    // console.log('pumpCollections', pumpCollections)
  }, [allPumpCollections, isAllPumpSuccess, fusion, election])
  const changeTab = (key: React.Key) => {
    const newTab = key as ITabs
    if (currentTab !== newTab) {
      clearTabFilters(currentTab)
    }
    updateSearchParams({ type: newTab })
    setCurrentTab(key as ITabs)
  }

  const tabPage = {
    createPump: (
      <PumpNfts
        address={address as IAddress}
        homeClass={isPhone ? 'sticky top-[120px] z-30 bg-alphaWhite100' : ''}
        homeTab="createPump"
        tabPrefix="createPump"
      />
    ),
    tokens: (
      <PumpTokens address={address as IAddress} homeClass={isPhone ? 'sticky top-[120px] z-30 bg-alphaWhite100' : ''} />
    ),
    nfts: <Nfts address={address as IAddress} />,
    onSale: <OnSale address={address as IAddress} />,
    collections: <Collectons />,
    created: <Created />,
    activity: (
      <Activity
        className={{ base: 'py-0', btn: 'pt-0' }}
        graphQl={{ ...user_graph }}
        type="user"
        otherAddress={address}
        topVariables={{
          ownerId: address,
          contractAddress_in: ownerContracts as IAddress[],
        }}
      />
    ),
  }

  const currentPage = tabPage[currentTab]

  return (
    <UserGraphContext.Provider
      value={{
        refetchUserGraph: () => {
          allCahche?.()
          saleCache?.()
          allReftch?.()
          saleReftch?.()
        },
        pumpContracts,
        ownerContracts,
        allPumpContracts,
      }}
    >
      <div>
        {!isPhone ? <BackComp /> : null}
        <UserCover />
        <div className={twMerge('border-b border-borderPrimary pt-8', isPhone && 'hidden')}>
          <Tab
            selectedKey={currentTab}
            classNames={{ tabList: 'gap-0', base: 'w-full' }}
            list={tabs}
            handleTab={changeTab}
          />
        </div>
        <Spacer y={8} className={twMerge(!isPhone && 'hidden')} />
        <div className={twMerge('sticky top-[72px] z-30 bg-alphaWhite100', !isPhone && 'hidden')}>
          <div className="relative overflow-y-hidden">
            <div
              className={twMerge(
                'pointer-events-none absolute inset-0 z-[30] h-full w-full',
                atRight && 'hidden',
                isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
              )}
            />
            <div
              className={twMerge(
                'pointer-events-none absolute inset-0 z-[30] h-full w-full rotate-180',
                atLeft && 'hidden',
                isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
              )}
            />
            <div ref={elementRef} className="overflow-x-auto scrollbar-hide">
              <TabWithMore
                selectedKey={currentTab}
                visibleCount={3}
                classNames={{ tabList: 'gap-0', base: 'w-full', tab: 'px-1.5 py-0 pb-1 h-6' }}
                list={tabs}
                handleTab={changeTab}
              />
            </div>
          </div>
        </div>
        <div
          className={twMerge(
            'sticky top-[96px] z-30 h-6 w-full bg-alphaWhite100',
            (!isPhone || !['createPump', 'tokens'].includes(currentTab)) && 'hidden'
          )}
        ></div>
        <div className={isPhone && ['createPump', 'tokens'].includes(currentTab) ? '' : 'py-6'}>{currentPage}</div>
      </div>
    </UserGraphContext.Provider>
  )
}

export default Profile
