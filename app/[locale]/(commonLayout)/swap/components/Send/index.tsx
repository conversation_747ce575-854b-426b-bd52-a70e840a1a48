import React, { useEffect, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { Currency } from '@uniswap/sdk-core'
import { ethers } from 'ethers'

import Alg2 from '@/components/Icon/Alg2'
import CIQI from '@/components/Icon/CIQI'
import Button from '@/components/library/Button'
import Input from '@/components/library/Input'

import useSwapBalance from '../Swap/hooks/useSwapBalance'
import useTransfer from './hooks/useTransfer'
import { CIQI_TOKEN } from '@/app/[locale]/(commonLayout)/pool/constants'
import useConnect from '@/hooks/useConnect'
import { useToast } from '@/hooks/useToast'
import { formatNumberWithZeroCount } from '@/libs/common/format'
import { compareStringNumbers } from '@/libs/common/math'
import { useAppKitAccount } from '@/libs/contract/wagmiConf'
import { checkNumberAndFormatZero } from '@/libs/vaildata/number'
import { IAddress } from '@/types/IAddress'

const Send = () => {
  const { t } = useTranslate('swap')
  const { t: tCommon } = useTranslate('common')
  const { address } = useAppKitAccount()
  const { withConnect } = useConnect()
  const { balance, refetch } = useSwapBalance(CIQI_TOKEN as Currency, address as IAddress)
  const [amount, setAmount] = useState<string>('')
  const [transferAddress, setTransferAddress] = useState<string>('')
  const { transfer, isSuccess, isError, errorMessage, isLoading } = useTransfer()
  const [disable, setDisable] = useState<boolean>(true)
  const { successToast, errorToast } = useToast()
  useEffect(() => {
    if (transferAddress && amount && compareStringNumbers(amount, balance.toString()) <= 0) {
      const isAddress = ethers.isAddress(transferAddress)
      if (isAddress) {
        setDisable(false)
      } else {
        setDisable(true)
      }
    } else {
      setDisable(true)
    }
  }, [transferAddress, amount, balance])
  const transferAction = withConnect(() => {
    transfer?.(transferAddress as IAddress, amount)
  })
  useEffect(() => {
    if (isLoading) {
      setDisable(true)
    } else {
      setDisable(false)
    }
  }, [isLoading])
  useEffect(() => {
    if (isSuccess) {
      successToast(t('transferSuccess'))
      refetch?.()
    }
  }, [isSuccess, t])
  useEffect(() => {
    if (isError && errorMessage) {
      errorToast(errorMessage === 'User rejected the request.' ? tCommon('user_rejected_request') : errorMessage)
    }
  }, [isError, errorMessage])
  return (
    <div className="flex w-full flex-col items-start rounded-2xl border-1 border-solid border-borderPrimary px-6 pb-6 pt-4">
      <div className="flex w-full flex-col items-start px-0 pb-4 pt-2">
        <div className="flex w-full flex-row justify-between gap-3 p-0">
          <div className="flex flex-row items-center justify-center gap-[6px] px-1 pb-3 pt-0 text-2xl font-semibold text-primary">
            {t('send')}
          </div>
          <div className="flex flex-row items-center p-0">
            <div className="flex flex-row items-center justify-center gap-[6px] px-3 py-[6px]">
              <Alg2 className="h-6 w-6" />
            </div>
          </div>
        </div>
      </div>
      <div className="flex w-full flex-col gap-2.5 rounded-xl bg-buttonTertiary px-2 py-4">
        <div className="flex items-center gap-2">
          <CIQI />
          <span className="text-lg text-primary">CIQI</span>
        </div>
        <span className=" text-xs text-quaternary5">
          {' '}
          {t('Balance', { ns: 'swap', balance: formatNumberWithZeroCount(balance || 0), symbol: CIQI_TOKEN?.symbol })}
        </span>
      </div>
      <div className="flex w-full flex-col items-start justify-center gap-3 px-0 py-4">
        <div className="flex w-full flex-col items-start gap-1 p-0">
          <span className="text-sm font-medium text-primary">{t('Amount')}</span>
          <Input
            className="w-full justify-between rounded bg-buttonTertiary"
            inputClassName="w-full text-start text-primary text-sm font-medium"
            placeholder="0"
            suffix={
              <div
                className="min-w-[56px] cursor-pointer text-end text-sm text-quaternary hover:text-primary"
                onClick={() => {
                  const inputValue = balance.toString() || '0'
                  const formattedValue = formatNumberWithZeroCount(inputValue)
                  setAmount(formattedValue || '')
                }}
              >
                {t('MAX')}
              </div>
            }
            type="number"
            value={amount}
            onChange={(e) => {
              if (e.target instanceof HTMLInputElement) {
                const value = e.target?.value
                const result = checkNumberAndFormatZero(value)
                if (!result) return
                setAmount(e.target?.value)
              }
            }}
          />
        </div>
        <div className="flex w-full flex-col items-start gap-1 p-0">
          <span className="text-sm font-medium text-primary">{t('Receiver Address')}</span>
          <Input
            className="w-full justify-between rounded bg-buttonTertiary "
            inputClassName="w-full text-start text-primary text-sm font-medium"
            value={transferAddress}
            onChange={(e) => {
              if (e.target instanceof HTMLInputElement) {
                setTransferAddress(e.target?.value)
              }
            }}
          />
        </div>
      </div>
      <Button
        className="w-full"
        onClick={transferAction}
        size="lg"
        disabled={disable || !amount || Number(amount) === 0}
      >
        {t('Transfer')}
      </Button>
    </div>
  )
}

export default Send
