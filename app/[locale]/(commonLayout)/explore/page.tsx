'use client'

import React, { memo, useCallback, useMemo, useState } from 'react'

import { useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import { useSearchParams } from 'next/navigation'

import Tab from '@/components/library/Tab'

import Collections from './components/Collections'
import Nfts from './components/Nfts'
import Users from './components/Users'
import Pump404 from '@/app/[locale]/(commonLayout)/pump404/components/Nfts'
import { useClearTabFilters, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'

export type ITabKey = 'pump' | 'collection' | 'nfts' | 'user'
const Explore = () => {
  const queryClient = useQueryClient()
  const updateSearchParams = useUpdateSearchParams()
  const clearTabFilters = useClearTabFilters()
  const { t } = useTranslate(['explore', 'profile'])
  const searchParams = useSearchParams()
  const type = (searchParams.get('type') || 'nfts') as ITabKey
  const [selectKey, setSelectKey] = useState<ITabKey>(type)

  // useEffect(()=>{
  //   if (type === "nfts") {
  //     setSelectKey("nfts")
  //   }
  // }, [type])
  const tabs = [
    {
      key: 'pump',
      title: 'Tokens',
    },
    // {
    //   key: 'collection',
    //   title: t('profile_collections', { ns: 'profile' }),
    // },
    {
      key: 'nfts',
      title: 'NFTs',
    },

    {
      key: 'user',
      title: t('users', { ns: 'explore' }),
    },
  ]
  const explorePage = useMemo(() => {
    return {
      pump: <Pump404 isShowList={true} homeTab="tokens" tabPrefix="tokens" />,
      nfts: <Nfts />,
      collection: <Collections />,
      user: <Users />,
    }
  }, [])

  const tabPage = useMemo(() => {
    return explorePage[selectKey]
  }, [selectKey, explorePage])

  const changeTab = useCallback((key: React.Key) => {
    const newTab = key as ITabKey
    if (selectKey !== newTab) {
      clearTabFilters(selectKey)
    }
    updateSearchParams({ type: newTab })
    if (key == 'user') {
      queryClient.removeQueries({
        queryKey: ['userList'],
      })
    } else if (key == 'nfts') {
      queryClient.removeQueries({
        queryKey: ['exploreNfts'],
      })
    } else if (key == 'collection') {
      queryClient.removeQueries({
        queryKey: ['getHomeCollection'],
      })
    } else if (key == 'pump') {
      queryClient.removeQueries({
        queryKey: ['explorePumpTokens'],
      })
    }
    setSelectKey(key as ITabKey)
  }, [])
  return (
    <div className="flex flex-col gap-4 py-4">
      <Tab
        classNames={{
          cursor: 'hidden',
          tab: 'px-0',
          tabList: 'gap-4',
          tabContent: ' font-semibold text-2xl',
        }}
        list={tabs}
        selectedKey={selectKey}
        handleTab={(key) => changeTab(key)}
      />
      <div>{tabPage}</div>
    </div>
  )
}

export default memo(Explore)
