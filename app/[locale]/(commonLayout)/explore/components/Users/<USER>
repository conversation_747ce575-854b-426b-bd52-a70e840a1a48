import React, { memo, useEffect, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { useSearchParams } from 'next/navigation'

import SearchInput from '@/components/Input/SearchInput'

import useGetUserList from '@/hooks/query/user/useGetUserList'
import { useToast } from '@/hooks/useToast'
import { useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
import { findByUserOptions } from '@/request/api/user'
import { IUserInfo } from '@/request/type/user'

import UserList from '../UserList'

const Users = () => {
  const { isError, isLoading: isAllLoading, userList: allUserList, refetch } = useGetUserList()
  const updateSearchParams = useUpdateSearchParams()
  const searchParams = useSearchParams()
  const searchValue = searchParams.get('searchUser') || ''
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { t } = useTranslate('explore')
  const [userList, setUserList] = useState<IUserInfo[]>([])
  const { errorToast } = useToast()
  const onSearch = (value: string) => {
    updateSearchParams({ searchUser: value })
    if (value) {
      setIsLoading(true)
      findByUserOptions(value, value, value)
        .then((data) => {
          const newList = data as unknown as IUserInfo[]
          setUserList(newList)
          setIsLoading(false)
        })
        .catch((error) => {
          setIsLoading(false)
          errorToast(error?.msg || error?.message)
        })
    } else {
      setUserList(allUserList)
    }
  }
  useEffect(() => {
    if (searchValue) {
      setIsLoading(true)
      findByUserOptions(searchValue, searchValue, searchValue)
        .then((data) => {
          const newList = data as unknown as IUserInfo[]
          setUserList(newList)
          setIsLoading(false)
        })
        .catch((error) => {
          setIsLoading(false)
          errorToast(error?.msg || error?.message)
        })
    }
    if (allUserList?.length > 0 && !searchValue) {
      setUserList(allUserList)
    }
  }, [allUserList, searchValue])
  return (
    <div className="flex flex-col gap-5">
      <SearchInput onSearch={onSearch} placeholder={t('search_users')} defaultValue={searchValue} />
      <UserList userList={userList} isError={isError} isLoading={isLoading || isAllLoading} refetch={refetch} />
    </div>
  )
}

export default memo(Users)
