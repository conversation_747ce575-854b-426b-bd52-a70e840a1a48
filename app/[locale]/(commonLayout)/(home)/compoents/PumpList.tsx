'use client'

import React, { useEffect, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { useSearchParams } from 'next/navigation'

import Tab from '@/components/library/Tab'

import userState from '@/store/user-store'

import Nfts from '../../pump404/components/Nfts'
import { useClearTabFilters, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
import { useAppKit, useAppKitAccount } from '@/libs/contract/wagmiConf'

export type HomeTabKey = 'favorites' | 'trending' | 'new' | 'holding' | 'tokens' | 'createPump'

const PumpList = () => {
  const { t } = useTranslate('common')
  const searchParams = useSearchParams()
  const type = (searchParams.get('type') || 'trending') as HomeTabKey
  const updateSearchParams = useUpdateSearchParams()
  const clearTabFilters = useClearTabFilters()
  const [currentTab, setCurrentTab] = useState<HomeTabKey>(type)
  const modal = useAppKit()
  const setManualLogout = userState((state) => state.setManualLogout)
  const { isConnected } = useAppKitAccount()

  const handleTabChange = (key: React.Key) => {
    const newTab = key as HomeTabKey
    if (currentTab !== newTab) {
      clearTabFilters(currentTab)
    }
    updateSearchParams({ type: newTab })
    setCurrentTab(newTab)
  }

  const tabList = [
    {
      key: 'favorites',
      title: t('favorites'),
    },
    {
      key: 'trending',
      title: t('trending'),
    },
    {
      key: 'new',
      title: t('new'),
    },
    {
      key: 'holding',
      title: t('holding'),
    },
  ]

  useEffect(() => {
    const openConnectModal = () => {
      if (!isConnected) {
        setManualLogout(false)
        return modal.open()
      }
    }
    if (['favorites', 'holding'].includes(currentTab)) {
      openConnectModal()
    }
  }, [currentTab, isConnected])
  return (
    <div className="flex w-full flex-col ">
      <Tab
        selectedKey={currentTab}
        list={tabList}
        handleTab={handleTabChange}
        classNames={{
          tabList: 'gap-4',
          tab: 'px-0',
          cursor: 'hidden',
          tabContent: 'text-2xl font-semibold max-430:text-xl max-430:font-semibold',
          base: 'min-430:sticky min-430:top-[72px] py-4 min-430:bg-alphaWhite100 min-430:z-50 max-430:pt-6',
        }}
      />
      <Nfts
        key={currentTab}
        homeTab={currentTab}
        tabPrefix={currentTab}
        homeClass="min-430:sticky min-430:top-[136px] min-430:z-50 min-430:bg-alphaWhite100"
      />
    </div>
  )
}

export default PumpList
