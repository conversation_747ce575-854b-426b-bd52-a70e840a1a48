import React, { memo, useCallback, useContext, useEffect, useMemo, useState } from 'react'

import {
  <PERSON><PERSON><PERSON>,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  Switch,
  useDisclosure,
} from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'

import FollowSteps from '@/components/FollowSteps'
import CheckNumberInput from '@/components/Input/CheckNumberInput'
import DatePickerInput from '@/components/Input/DatePickerInput'
import Button from '@/components/library/Button'
import TipModal from '@/components/Modal/TipModal'

import { TokenDetailContext } from '../../../TokenDetailContext'
import useBid from './hook/useBid'
import { GraphContext } from '@/graphql/GraphContext'
import useFee from '@/hooks/useFee'
import { formatPrice } from '@/libs/common/format'
import { calculateTotalPrice } from '@/libs/common/math'
import { checkNumber, checkNumberAndFormatZero, checkNumberAndMoreZero } from '@/libs/vaildata/number'
import { IAddress } from '@/types/IAddress'

import AccountCard from './AccountCard'
import { ModalContext } from './ModalContext'
import RowInfo from './RowInfo'

interface IBidModalProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  openDepositModal?: () => void
  contractAddress?: IAddress
}

interface IBidModalResult {
  checked: boolean
  tips: string
}

const BidModal = (props: IBidModalProps) => {
  const { isOpen, onOpenChange, openDepositModal, contractAddress } = props
  const [isUpdateValue, setIsUpdateValue] = useState<boolean>(false)
  const [checkResult, setCheckResult] = useState<IBidModalResult>({ checked: true, tips: '' })
  const [isUserInput, setIsUserInput] = useState<boolean>(false)
  const { isOpen: isStepOpen, onOpen: onStepOpen, onOpenChange: onStepOpenChange, onClose } = useDisclosure()
  const { isOpen: isSuccessOpen, onOpen: onSuccessOpen, onOpenChange: onSuccessOpenChange } = useDisclosure()

  const { isUpdateBalance, setIsUpdateBalance, onBidSuccess } = useContext(ModalContext)
  const { tokenDetail } = useContext(TokenDetailContext)
  const fee = useFee()
  const { t } = useTranslate(['token', 'common'])
  const {
    followSteps,
    currentStep,
    errorIndex,
    errorMsg,
    priceValue,
    walgBalance,
    isMakeOfferSuccess,
    refetchWalgBalance,
    setExpiration,
    setPriceValue,
    resetStepInfo,
  } = useBid(contractAddress)
  const isShowDeposit = useMemo(
    () => Number(walgBalance) === 0 || Number(priceValue) > walgBalance,
    [walgBalance, priceValue]
  )
  const totalPrice = useMemo(() => {
    return calculateTotalPrice(priceValue, fee)
  }, [fee, priceValue])
  const { refetchGraph } = useContext(GraphContext)
  const { refreshBid } = useContext(TokenDetailContext)

  useEffect(() => {
    if (isMakeOfferSuccess) {
      onStepClose()
      onSuccessOpen()
      onBidSuccess()
      refetchGraph?.()
      refreshBid?.(true)
    }
  }, [isMakeOfferSuccess])

  useEffect(() => {
    if (isUpdateBalance) {
      refetchWalgBalance()
      setIsUpdateBalance(false)
    }
  }, [isUpdateBalance])

  useEffect(() => {
    if (isUpdateValue) {
      checkBindPrice(priceValue)
    }
  }, [walgBalance])

  const changeValue = (value: string) => {
    // 检查小数点后是否超过18位，且非零数字不超过5位
    setIsUserInput(true)
    const result = checkNumberAndFormatZero(value)
    if (!result) return
    setPriceValue(value)
    checkBindPrice(value)
    setIsUpdateValue(true)
  }

  const checkBindPrice = (value: string) => {
    const result = checkNumberAndMoreZero(value)
    if (Number(value) > walgBalance) {
      const result = {
        checked: false,
        tips: t('tips_balance_not_enough', { ns: 'common', token: 'WALG' }),
      }
      setCheckResult(result)
      return
    }
    setCheckResult({
      checked: result.checked,
      tips: t(result.tips, { ns: 'common' }),
    })
  }

  const closeModal = () => {
    setPriceValue('')
  }

  const changeExpiration = (date: Date) => {
    setExpiration(date)
  }

  const openStepModal = () => {
    onOpenChange(false)
    onStepOpen()
    followSteps[0].action()
  }

  const tryStepAction = useCallback(
    (index: number) => {
      followSteps[index].action()
    },
    [followSteps]
  )

  const onStepClose = useCallback(() => {
    resetStepInfo()
    onClose()
  }, [])

  return (
    <div>
      {isOpen && (
        <Modal
          size="sm"
          isOpen={isOpen}
          classNames={{ closeButton: ' rounded-md right-3.5 top-3.5' }}
          onOpenChange={onOpenChange}
          onClose={closeModal}
        >
          <ModalContent>
            <ModalHeader className="flex flex-col gap-1">{t('bid_modal_title')}</ModalHeader>
            <ModalBody className="gap-6">
              <p className=" text-base text-quaternary">
                {t('bid_modal_note1')}
                <span className=" text-primary"> {tokenDetail?.baseInfo?.name} </span>
                {t('bid_modal_note2')}
                <span className=" text-primary"> {tokenDetail?.baseInfo?.collection?.name} </span>
                {t('bid_modal_note3')}
              </p>
              <AccountCard />
              <div className="flex flex-col gap-4">
                <CheckNumberInput
                  label={t('Bid price', { ns: 'token' })}
                  isUpterInput={isUpdateBalance}
                  defaultValue={priceValue}
                  changeValue={changeValue}
                  checkResult={checkResult}
                  isUserInput={isUserInput}
                />
                <div className="flex flex-col gap-1">
                  <span className=" text-sm">{t('bid_modal_expiration')}</span>
                  <DatePickerInput onChange={changeExpiration} />
                </div>
              </div>
              <RowInfo
                textClassName="text-primary"
                text={t('bid_modal_floor_bid')}
                value={<Switch disabled size="sm" />}
              />
              <div className="flex flex-col gap-1">
                <RowInfo text={t('bid_modal_bid_balance')} value={formatPrice(priceValue, 'WALG', 'symbol')} />
                <RowInfo text={t('bid_modal_balance')} value={formatPrice(walgBalance, 'WALG', 'symbol')} />
                <RowInfo text={t('buy_modal_fee')} showIcon value={`${fee * 100}%`} />
                <Divider className="my-2 bg-borderPrimary" />
                <RowInfo text={t('bid_modal_total_price')} showIcon value={formatPrice(totalPrice, 'WALG', 'symbol')} />
              </div>
            </ModalBody>
            <ModalFooter>
              {isShowDeposit ? (
                <div className="flex w-full items-center gap-2">
                  <Button className="w-full" disabled>
                    {t('bid_modal_title')}
                  </Button>
                  <Button className="w-full" color="gray" onClick={openDepositModal}>
                    {t('bid_modal_add_token', { token: 'WALG' })}
                  </Button>
                </div>
              ) : (
                <Button
                  disabled={!checkNumber(priceValue) || Number(priceValue) === 0}
                  className="w-full"
                  onClick={openStepModal}
                >
                  {t('bid_modal_title')}
                </Button>
              )}
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
      {isStepOpen && (
        <FollowSteps
          isOpen={isStepOpen}
          followSteps={followSteps}
          currentStep={currentStep}
          errorIndex={errorIndex}
          errorMessage={errorMsg}
          onClose={onStepClose}
          onOpenChange={onStepOpenChange}
          onRetry={tryStepAction}
        />
      )}
      {isSuccessOpen && (
        <TipModal
          isOpen={isSuccessOpen}
          icon="🎉"
          isNextButton={false}
          title={t('success_modal_title')}
          content={t('success_modal_content')}
          onOpenChange={onSuccessOpenChange}
        />
      )}
    </div>
  )
}

export default memo(BidModal)
