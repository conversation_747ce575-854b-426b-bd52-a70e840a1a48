import React, { memo, useContext, useEffect, useMemo, useState } from 'react'

import { <PERSON><PERSON><PERSON>, Modal, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, Switch } from '@nextui-org/react'
import { useTranslate } from '@tolgee/react'
import { useAccount } from 'wagmi'

import CheckNumberInput from '@/components/Input/CheckNumberInput'
import DatePickerInput from '@/components/Input/DatePickerInput'
import Button from '@/components/library/Button'

import { MAX_APPROVE } from '@/constant/global'
import useFusionAddress from '@/hooks/contract/address/useFusionAddress'
import useAllowance from '@/hooks/contract/token/useAllowance'
import useApprove from '@/hooks/contract/token/useApprove'
import useBalance from '@/hooks/contract/token/useBalance'
import useFee from '@/hooks/useFee'
import { useToast } from '@/hooks/useToast'
import { formatPrice } from '@/libs/common/format'
import { checkNumberAndFormatZero, checkNumberAndMoreZero } from '@/libs/vaildata/number'

import { DepositProvider } from '.'
import AccountCard from './AccountCard'
import RowInfo from './RowInfo'

interface IBidModalProps {
  isOpen: boolean
  collectionName: string
  nftName: string
  onOpenChange: (isOpen: boolean) => void
  openDepositModal?: () => void
}

interface IBidModalResult {
  checked: boolean
  tips: string
}

const BidModal = (props: IBidModalProps) => {
  const { isOpen, nftName, collectionName, onOpenChange, openDepositModal } = props
  const [priceValue, setPriceValue] = useState<string>('')
  const [isUpdateValue, setIsUpdateValue] = useState<boolean>(false)
  const [isUserInput, setIsUserInput] = useState<boolean>(false)
  const [checkResult, setCheckResult] = useState<IBidModalResult>({ checked: true, tips: '' })
  const { isUpdateBalance, setIsUpdateBalance } = useContext(DepositProvider)

  const fee = useFee()
  const fusionAddress = useFusionAddress()
  const { errorToast, successToast } = useToast()
  const { address } = useAccount()
  const { t } = useTranslate(['token', 'common'])
  const { balance, refetch: refetchWalgBalance } = useBalance(address!, 'walg')
  const { allowance, refetch: refetchAllowance } = useAllowance(address!, fusionAddress, 'walg')
  const { approve, isError, isLoading, isSuccess, errorMessage } = useApprove()
  const isShowDeposit = useMemo(() => balance === 0 || Number(priceValue) > balance, [balance, priceValue])
  const isShowApprove = useMemo(() => allowance === 0 || Number(priceValue) > allowance, [allowance, priceValue])
  const totalPrice = useMemo(() => Number(priceValue) * (1 + fee), [fee, priceValue])

  useEffect(() => {
    if (isUpdateBalance) {
      refetchWalgBalance()
      setIsUpdateBalance(false)
    }
  }, [isUpdateBalance])

  useEffect(() => {
    if (isUpdateValue) {
      checkBindPrice(priceValue)
    }
  }, [balance])

  useEffect(() => {
    if (isSuccess) {
      successToast(t('bid_modal_approve_success'))
      refetchAllowance()
    }
  }, [isSuccess])

  useEffect(() => {
    if (isError) {
      console.log(errorMessage)

      errorToast(t('bid_modal_approve_fail'))
    }
  }, [isError])

  const changeValue = (value: string) => {
    setIsUserInput(true)
    // 检查小数点后是否超过18位，且非零数字不超过5位
    const result = checkNumberAndFormatZero(value)
    if (!result) return
    setPriceValue(value)
    checkBindPrice(value)
    setIsUpdateValue(true)
  }

  const checkBindPrice = (value: string) => {
    const result = checkNumberAndMoreZero(value)
    if (Number(value) > balance) {
      console.log('🚀 ~ checkBindPrice ~ balance:', balance)
      const result = {
        checked: false,
        tips: t('tips_balance_not_enough', { ns: 'common', token: 'WALG' }),
      }
      setCheckResult(result)
      return
    }
    setCheckResult({
      checked: result.checked,
      tips: t(result.tips, { ns: 'common' }),
    })
  }

  const closeModal = () => {
    setPriceValue('')
  }

  const bidNft = () => {
    console.log('bid nft')
  }

  const approveWalg = () => {
    approve(fusionAddress, MAX_APPROVE as unknown as string, 'walg')
  }

  return (
    <Modal
      size="sm"
      isOpen={isOpen}
      classNames={{ closeButton: ' rounded-md right-3.5 top-3.5' }}
      onOpenChange={onOpenChange}
      onClose={closeModal}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">{t('bid_modal_title')}</ModalHeader>
        <ModalBody className="gap-6">
          <p className=" text-base text-quaternary">
            {t('bid_modal_note1')}
            <span className=" text-primary"> {nftName} </span>
            {t('bid_modal_note2')}
            <span className=" text-primary"> {collectionName} </span>
            {t('bid_modal_note3')}
          </p>
          <AccountCard />
          <div className="flex flex-col gap-4">
            <CheckNumberInput
              label={t('Bid price', { ns: 'token' })}
              isUpterInput={isUpdateBalance}
              defaultValue={priceValue}
              changeValue={changeValue}
              checkResult={checkResult}
              isUserInput={isUserInput}
            />
            <div className="flex flex-col gap-1">
              <span className=" text-sm">{t('bid_modal_expiration')}</span>
              <DatePickerInput />
            </div>
          </div>
          <RowInfo textClassName="text-primary" text={t('bid_modal_floor_bid')} value={<Switch size="sm" />} />
          <div className="flex flex-col gap-1">
            <RowInfo text={t('bid_modal_bid_balance')} value={formatPrice(priceValue, 'WALG', 'symbol')} />
            <RowInfo text={t('bid_modal_balance')} value={formatPrice(balance, 'WALG', 'symbol')} />
            <RowInfo text={t('buy_modal_fee')} showIcon value={`${fee * 100}%`} />
            <Divider className="my-2 bg-borderPrimary" />
            <RowInfo text={t('bid_modal_total_price')} showIcon value={formatPrice(totalPrice, 'WALG', 'symbol')} />
          </div>
        </ModalBody>
        <ModalFooter>
          {isShowApprove && (
            <Button className="w-full" onClick={approveWalg} isLoading={isLoading}>
              {t('bid_modal_approve', { token: 'WALG' })}
            </Button>
          )}
          {!isShowApprove && isShowDeposit && (
            <div className="flex w-full items-center gap-2">
              <Button className="w-full" disabled>
                {t('bid_modal_title')}
              </Button>
              <Button className="w-full" color="gray" onClick={openDepositModal}>
                {t('bid_modal_add_token', { token: 'WALG' })}
              </Button>
            </div>
          )}
          {!isShowApprove && !isShowDeposit && (
            <Button disabled={priceValue === '' || priceValue === '0'} className="w-full" onClick={bidNft}>
              {t('bid_modal_title')}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}

export default memo(BidModal)
