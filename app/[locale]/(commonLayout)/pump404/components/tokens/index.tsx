'use client'

import React, { forwardRef, Key, memo, useEffect, useImperativeHandle, useMemo, useState } from 'react'
import { TbLayoutListFilled, TbListNumbers } from 'react-icons/tb'
import { TbLayoutGridFilled } from 'react-icons/tb'
import { TbStack2 } from 'react-icons/tb'
import { useInView } from 'react-intersection-observer'

import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslate } from '@tolgee/react'
import { twMerge } from 'tailwind-merge'
import { useMediaQuery } from 'usehooks-ts'

import EmptyComp from '@/components/EmptyComp'
import ErrorAndTryComp from '@/components/ErrorAndTryComp'
import { FilterContext, IFilterContext } from '@/components/Filters/FilterContext'
import FilterPanel, { IFilterPanel } from '@/components/Filters/FilterPanel'
import AlgL2 from '@/components/Icon/AlgL2'
import BaseChainBlack from '@/components/Icon/base-black'
import BscBlack from '@/components/Icon/bsc-black'
import SolanaWhite from '@/components/Icon/solana-white'
import SearchInput from '@/components/Input/SearchInput'
import CardListSkeleton from '@/components/library/Skeleton/PumpCardListSkeleton'
import CardSkeleton from '@/components/library/Skeleton/PumpCardSkeleton'
import TableSkeleton from '@/components/library/Skeleton/TableSkeleton'
import Tab from '@/components/library/Tab'
import NftCard from '@/components/Pump404/card/card-col'

// import userState from '@/store/user-store'
import { useNFTTableColumns } from '../../hooks/useNFTTableColumns'
import useScrollPosition from '../../hooks/useScrollPosition'
import { PumpTokenContext } from '../../PumpTokenContext'
import NftTable, { CapChangeType } from '../Nfts/nft-table'
import { CapChangeKeyMap, listPageSize, PageSize } from '@/constant/pump'
import { GraphContext } from '@/graphql/GraphContext'
import useGetMultiCurrentPrice from '@/hooks/contract/pump404/useGetMultiCurrentPrice'
import useBaseChain from '@/hooks/useBaseChain'
import useBSCChain from '@/hooks/useBSCChain'
import { isEVMChain } from '@/hooks/useIsEvm'
import useSolanaChain from '@/hooks/useSolanaChain'
import useTheme from '@/hooks/useTheme'
import { useReadSearchParams, useUpdateSearchParams } from '@/hooks/useUpdateSearchParams'
import { cn } from '@/lib/utils'
import { formatNumberWithUnicodeSubscript } from '@/libs/common/format'
import { supportChainId } from '@/libs/contract/wagmiConf'
import { getPumpTokens } from '@/request/api/pump'
import { IAddress } from '@/types/IAddress'
import { IGetPumpTokensReq, IPumpToken, TCapChange, TSortTypeKey, TTimeFrame } from '@/types/IPump'
import { DisplayMode } from '@/types/nft'

import Dropdown, { IDropdownItemProps } from '../DropDown'

const dateTabs = [
  {
    key: '5MIN',
    title: '5M',
  },
  {
    key: '1H',
    title: '1H',
  },
  {
    key: '6H',
    title: '6H',
  },
  {
    key: '1D',
    title: '24H',
  },
]

const filterTypeMap = {
  all: 'ALL',
  finalized: 'FINALIZED',
}

const sortTypeMap = {
  onehour: '1H',
  fiveminutes: '5MIN',
  sixhours: '6H',
  oneday: '1D',
  newest: 'NEWEST',
}
const timeSortMap = {
  fiveminutes: 'fiveMinuteCapChange',
  sixhours: 'sixHourCapChange',
  oneday: 'oneDayCapChange',
  onehour: 'oneHourCapChange',
  newest: undefined,
}

const filterPanelOptions: IFilterPanel[] = ['pumpblockchain']
interface IPumpNftsProps {
  address?: IAddress
  homeClass?: string
}
const Tokens = forwardRef(({ address, homeClass }: IPumpNftsProps, nftsRef) => {
  // const isSignIn = userState((state) => state.isSignIn)
  const { getTokenListWithPrice } = useGetMultiCurrentPrice()
  const updateSearchParams = useUpdateSearchParams('tokens')
  const readSearchParams = useReadSearchParams('tokens')
  const baseChainId = useBaseChain()
  const bscChain = useBSCChain()
  const solanaChain = useSolanaChain()
  const { elementRef, atLeft, atRight } = useScrollPosition()
  const { isDark } = useTheme()
  const queryClient = useQueryClient()
  const { ref, inView } = useInView({
    threshold: 1, // Trigger when fully visible
  })
  // const isMax635 = useMediaQuery('(max-width: 635px)')
  // const isMiniPhone = useMediaQuery('(max-width: 390px)')
  const isPhone = useMediaQuery('(max-width: 768px)')
  const isPad = useMediaQuery('(max-width: 1024px)')
  const columns = useNFTTableColumns(isPhone, 'sixHourCapChange')
  const [displayMode, setDisplayMode] = useState<DisplayMode>(() => {
    const urlParams = readSearchParams()
    if (urlParams.displayMode) {
      return urlParams.displayMode
    }
    return DisplayMode.LIST
  })
  const [isFitlter, setIsFilter] = useState<boolean>(false)
  const [filterParams, setFilterParams] = useState<any>(() => {
    const urlParams = readSearchParams()
    const initialFilterParams: any = {}
    if (urlParams.chainId) {
      initialFilterParams.chainId = urlParams.chainId.toString()
    }
    return initialFilterParams
  })
  const { t } = useTranslate(['explore', 'filters', 'common'])
  const [selectedDropdown, setSelectedDropdown] = useState<TSortTypeKey>(() => {
    const urlParams = readSearchParams()
    if (urlParams.selectedDropdown) {
      return urlParams.selectedDropdown
    }
    return 'sixhours'
  })
  const [variables, setVariables] = useState<IGetPumpTokensReq>(() => {
    const urlParams = readSearchParams()
    const initialVars: IGetPumpTokensReq = {
      search: '',
      filterType: 'ALL',
      timeFrame: '6H',
      ...urlParams,
    }
    return initialVars
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [tableData, setTableData] = useState<{ items: any[]; total: number }>({ items: [], total: 0 })
  const [isTableLoading, setIsTableLoading] = useState(false)
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isFetching, isError, refetch } = useInfiniteQuery({
    queryKey: ['explorePumpTokens', variables, PageSize, address, displayMode],
    queryFn: async ({ pageParam }) => {
      const params = {
        ...variables,
        page: pageParam,
        pageSize: PageSize,
      }
      const { chainId } = variables
      updateSearchParams({ ...variables, displayMode, chainId: chainId || 'all', type: 'tokens' })
      if (address) {
        params['userAddress'] = address
      }
      const {
        data: { items, total },
      } = await getPumpTokens(params)
      const itemsWithPrice = (await getTokenListWithPrice(items)) || []
      const newItems = itemsWithPrice.map((token: IPumpToken) => {
        return {
          ...token,
          capChange: token[CapChangeKeyMap[variables.timeFrame as keyof typeof CapChangeKeyMap] as TCapChange],
          marketCap: formatNumberWithUnicodeSubscript(token.marketCap, '$'),
        }
      })
      return { items: newItems, total }
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const { total } = lastPage
      const maxPage = Math.ceil(total / PageSize)
      const hasMore = allPages.length < maxPage
      return hasMore ? allPages.length + 1 : null
    },
    // enabled: !!variables,
  })

  const fetchTableData = async (page: number) => {
    setIsTableLoading(true)
    try {
      const params = {
        ...variables,
        page,
        pageSize: listPageSize,
      }
      if (address) {
        params['userAddress'] = address
      }
      const {
        data: { items, total },
      } = await getPumpTokens(params)
      const itemsWithPrice = (await getTokenListWithPrice(items)) || []
      const newItems = itemsWithPrice.map((token: IPumpToken) => {
        return {
          ...token,
          capChange: token[CapChangeKeyMap[variables.timeFrame as keyof typeof CapChangeKeyMap] as TCapChange],
          marketCap: formatNumberWithUnicodeSubscript(token.marketCap, '$'),
        }
      })
      setTableData({ items: newItems, total })
    } catch (error) {
      console.error('Failed to fetch table data:', error)
    } finally {
      setIsTableLoading(false)
    }
  }

  const iconTabs = useMemo(() => {
    if (isPhone) {
      return [
        {
          key: DisplayMode.SMALL,
          title: <TbLayoutListFilled />,
        },
        {
          key: DisplayMode.LIST,
          title: <TbListNumbers />,
        },
      ]
    } else {
      return [
        {
          key: DisplayMode.LARGE,
          title: <TbLayoutGridFilled />,
        },
        {
          key: DisplayMode.SMALL,
          title: <TbLayoutListFilled />,
        },
        {
          key: DisplayMode.LIST,
          title: <TbListNumbers />,
        },
      ]
    }
  }, [isPhone])
  useEffect(() => {
    if (displayMode === DisplayMode.LIST) {
      fetchTableData(currentPage)
    }
  }, [displayMode, variables, currentPage, address])
  const handleTablePageChange = (page: number) => {
    setCurrentPage(page)
  }

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [inView, hasNextPage, fetchNextPage, isFetchingNextPage])

  const chainIdTab = [
    {
      key: 'all',
      title: <TbStack2 />,
    },
    {
      key: `${supportChainId}`,
      title: <AlgL2 />,
    },
    {
      key: `${baseChainId}`,
      title: <BaseChainBlack />,
    },
    {
      key: `${bscChain}`,
      title: <BscBlack className="h-5 w-5" />,
    },
    {
      key: `${solanaChain}`,
      title: <SolanaWhite className="h-5 w-5" />,
    },
  ]
  const filterTypeTab = useMemo(
    () => [
      {
        key: 'all',
        title: t('all', { ns: 'common' }),
      },
      {
        key: 'finalized',
        title: t('finalized', { ns: 'common' }),
      },
    ],
    [t]
  )

  const filterDropdownList: IDropdownItemProps[] = useMemo(() => {
    return [
      {
        key: 'newest',
        value: t('rankByNew', { ns: 'common' }),
      },
      {
        key: 'fiveminutes',
        value: t('rankBy5M', { ns: 'common' }),
      },
      {
        key: 'onehour',
        value: t('rankBy1H', { ns: 'common' }),
      },
      {
        key: 'sixhours',
        value: t('rankBy6H', { ns: 'common' }),
      },
      {
        key: 'oneday',
        value: t('rankBy24H', { ns: 'common' }),
      },
    ]
  }, [t])
  const filterContext: IFilterContext = {
    displayMode,
    setDisplayMode,
    isFilter: isFitlter,
    changeFilter: setIsFilter,
    filterParams: filterParams,
    changeFilterParams: setFilterParams,
  }
  useImperativeHandle(
    nftsRef,
    () => {
      return {
        refetch: () => {
          refetch()
        },
      }
    },
    [refetch]
  )
  const handleTab = (key: keyof IGetPumpTokensReq, value: React.Key) => {
    setCurrentPage(1)
    if (key === 'timeFrame') {
      const timeFrameToDropdownMap: Record<string, string> = {
        '5MIN': 'fiveminutes',
        '1H': 'onehour',
        '6H': 'sixhours',
        '1D': 'oneday',
      }
      setSelectedDropdown(timeFrameToDropdownMap[value as string] as TSortTypeKey)
      delete variables.sortType
    }
    setVariables({ ...variables, [key]: key === 'filterType' ? filterTypeMap[value as 'all' | 'finalized'] : value })
  }

  const onDropDown = (value: string) => {
    setCurrentPage(1)
    setSelectedDropdown(value as TSortTypeKey)
    if (value === 'newest') {
      delete variables.timeFrame
      setVariables({
        ...variables,
        sortType: 'NEWEST',
      })
    } else {
      delete variables.sortType
      setVariables({
        ...variables,
        timeFrame: sortTypeMap[value as TSortTypeKey] as TTimeFrame,
      })
    }
  }

  const onSearchNft = (value: any) => {
    setCurrentPage(1)
    if (value) {
      setVariables({
        ...variables,
        search: value,
      })
    } else {
      delete variables.search
      setVariables({
        ...variables,
      })
    }
  }
  useEffect(() => {
    const urlParams = readSearchParams()
    if (Object.keys(urlParams).length > 0) {
      const newVariables = { ...variables }
      if (urlParams.search !== undefined) newVariables.search = urlParams.search
      if (urlParams.filterType !== undefined) newVariables.filterType = urlParams.filterType
      if (urlParams.timeFrame !== undefined) newVariables.timeFrame = urlParams.timeFrame
      if (urlParams.sortType !== undefined) newVariables.sortType = urlParams.sortType
      if (urlParams.chainId !== undefined && urlParams.chainId !== 'all') newVariables.chainId = urlParams.chainId
      if (urlParams.onlyFavorite !== undefined) newVariables.onlyFavorite = urlParams.onlyFavorite
      if (urlParams.userAddress !== undefined) newVariables.userAddress = urlParams.userAddress
      setVariables(newVariables)
      if (urlParams.selectedDropdown) {
        setSelectedDropdown(urlParams.selectedDropdown)
      }
      const newFilterParams = { ...filterParams }
      if (urlParams.chainId) {
        newFilterParams.chainId = urlParams.chainId.toString()
      }
      setFilterParams(newFilterParams)
      if (urlParams.displayMode) {
        setDisplayMode(urlParams.displayMode)
      }
    }
  }, [])
  useEffect(() => {
    setCurrentPage(1)
    if (variables) {
      queryClient.invalidateQueries({ queryKey: ['explorePumpTokens', variables] })
    }
  }, [queryClient, variables])

  useEffect(() => {
    setCurrentPage(1)
    if (filterParams.chainId) {
      if (filterParams.chainId === 'all') {
        delete variables.chainId
        setVariables({
          ...variables,
        })
      } else {
        setVariables({
          ...variables,
          chainId: isEVMChain(filterParams.chainId) ? Number(filterParams.chainId) : filterParams.chainId.toString(),
        })
      }
    }
  }, [filterParams])

  return (
    <GraphContext.Provider
      value={{
        refetchGraph: refetch,
      }}
    >
      <FilterContext.Provider value={filterContext}>
        <div className="flex flex-col">
          {/* {!isMax635 ? (
            <div className="relative h-10 overflow-y-hidden">
              <div
                className={cn(
                  'pointer-events-none absolute inset-0 z-[10] h-[40px] w-full',
                  atRight && 'hidden',
                  isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
                )}
              />
              <div
                className={cn(
                  'pointer-events-none absolute inset-0 z-[10] h-[40px] w-full rotate-180',
                  atLeft && 'hidden',
                  isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
                )}
              />
              <div className="oveflow-y-hidden flex h-[60px] items-start gap-4 overflow-x-auto" ref={elementRef}>
                <div className="flex flex-row items-center justify-center">
                  <Tab
                    variants="solid"
                    classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                    list={chainIdTab}
                    handleTab={(key: React.Key) => setFilterParams?.({ ...filterParams, chainId: key })}
                  />
                </div>
                <div className="flex flex-row items-center justify-center">
                  <Tab
                    variants="solid"
                    classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                    list={dateTabs}
                    handleTab={(value: React.Key) => handleTab('timeFrame', value)}
                  />
                </div>
                <div className="flex flex-row items-center">
                  <Tab
                    variants="solid"
                    classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                    list={filterTypeTab}
                    handleTab={(value: React.Key) => handleTab('filterType', value)}
                  />
                </div>
                <SearchInput
                  placeholder={t('search_tokens', { ns: 'explore' })}
                  onSearch={onSearchNft}
                  debounceTime={500}
                  classNames={{
                    base: 'min-w-[180px]',
                  }}
                />
                <div className="flex items-center gap-1.5">
                  <div className="block">
                    <Dropdown list={filterDropdownList} onAction={onDropDown} transitionKey="common" />
                  </div>
                  <div className="block">
                    <Tab
                      variants="solid"
                      classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                      list={iconTabs}
                      selectedKey={displayMode}
                      handleTab={(key: Key) => {
                        setDisplayMode(key as DisplayMode)
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex  flex-wrap gap-4 ">
              <div className={twMerge('item-center jusity-center flex flex-row', isMiniPhone && 'flex-1')}>
                <Tab
                  variants="solid"
                  classNames={{
                    base: isMiniPhone ? 'w-full' : '',
                    tabList: '!px-1 !py-1 w-full',
                    tab: 'first:!pl-3 last:!pr-3',
                  }}
                  list={dateTabs}
                  handleTab={(value: React.Key) => handleTab('timeFrame', value)}
                />
              </div>
              <div className="flex flex-row items-center">
                <Tab
                  variants="solid"
                  classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                  list={filterTypeTab}
                  handleTab={(value: React.Key) => handleTab('filterType', value)}
                />
              </div>
              <SearchInput
                placeholder={t('search_tokens', { ns: 'explore' })}
                onSearch={onSearchNft}
                debounceTime={500}
                classNames={{ base: 'w-[180px] w-full' }}
              />
              <Button color="gray" className="min-w-10 !p-0 md:hidden" onClick={() => setIsFilter(!isFitlter)}>
                {isFitlter ? (
                  <LuChevronLeft size={20} className=" text-primary" />
                ) : (
                  <TbFilter size={20} className=" text-primary" />
                )}
              </Button>
              <Dropdown list={filterDropdownList} onAction={onDropDown} isSimpleButton={true}></Dropdown>
            </div>
          )} */}
          <div className={twMerge('relative h-10 overflow-y-hidden', homeClass)}>
            <div
              className={cn(
                'pointer-events-none absolute inset-0 z-[10] h-[40px] w-full',
                atRight && 'hidden',
                isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
              )}
            />
            <div
              className={cn(
                'pointer-events-none absolute inset-0 z-[10] h-[40px] w-full rotate-180',
                atLeft && 'hidden',
                isDark ? 'pump-tab-mask_dark' : 'pump-tab-mask_white'
              )}
            />
            <div className="oveflow-y-hidden flex h-[60px] items-start gap-4 overflow-x-auto" ref={elementRef}>
              <div className="flex flex-row items-center justify-center">
                <Tab
                  variants="solid"
                  classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                  list={chainIdTab}
                  selectedKey={filterParams.chainId || 'all'}
                  handleTab={(key: React.Key) => setFilterParams?.({ ...filterParams, chainId: key })}
                />
              </div>
              <div className="flex flex-row items-center justify-center">
                <Tab
                  variants="solid"
                  classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                  list={dateTabs}
                  selectedKey={variables.timeFrame}
                  notMatch={selectedDropdown === 'newest'}
                  handleTab={(value: React.Key) => handleTab('timeFrame', value)}
                />
              </div>
              <div className="flex flex-row items-center">
                <Tab
                  variants="solid"
                  classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                  list={filterTypeTab}
                  selectedKey={variables.filterType === 'ALL' ? 'all' : 'finalized'}
                  handleTab={(value: React.Key) => handleTab('filterType', value)}
                />
              </div>
              <SearchInput
                placeholder={t('search_tokens', { ns: 'explore' })}
                defaultValue={variables.search || ''}
                onSearch={onSearchNft}
                debounceTime={500}
                classNames={{
                  base: 'min-w-[240px]',
                }}
              />
              <div className="flex items-center gap-1.5">
                <div className="block">
                  <Dropdown
                    list={filterDropdownList}
                    defaultValue={selectedDropdown}
                    onAction={onDropDown}
                    transitionKey="common"
                    isPumpFilter={true}
                  />
                </div>
                <div className="block">
                  <Tab
                    variants="solid"
                    classNames={{ tabList: '!px-1 !py-1', tab: 'first:!pl-3 last:!pr-3' }}
                    list={iconTabs}
                    selectedKey={displayMode}
                    handleTab={(key: Key) => {
                      setDisplayMode(key as DisplayMode)
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-row items-start gap-4 p-0">
            {isFitlter && (
              <div className="top-20 lg:sticky">
                <FilterPanel onClose={() => setIsFilter(false)} options={filterPanelOptions} />
              </div>
            )}
            {/* nfts */}
            <div className="w-full py-5">
              {data?.pages?.[0]?.items?.length && data?.pages?.[0]?.items?.length > 0 ? (
                <>
                  {displayMode === DisplayMode.LIST ? (
                    <div className="w-full">
                      <NftTable
                        isPhone={isPhone}
                        key={`${currentPage}`}
                        nftData={tableData.items}
                        total={tableData.total}
                        currentPage={currentPage}
                        pageSize={listPageSize}
                        onPageChange={handleTablePageChange}
                        isPagination={true}
                        isLoading={isTableLoading}
                        selectedCapChange={timeSortMap[selectedDropdown] as CapChangeType}
                      />
                    </div>
                  ) : (
                    <div
                      className={twMerge(
                        isPhone ? 'flex flex-col gap-3' : 'grid w-full gap-3',
                        data?.pages?.[0]?.items?.length === 0 && 'hidden'
                      )}
                      style={{
                        gridTemplateColumns: !isPhone
                          ? `repeat(auto-fill, minmax(${displayMode === DisplayMode.SMALL ? '456px' : isPad ? '248px' : '262px'}, 1fr))`
                          : undefined,
                      }}
                    >
                      {data?.pages.map((page: any) =>
                        page.items.map((nft: any) => (
                          <PumpTokenContext.Provider value={{ tokenDetail: nft }} key={nft.id}>
                            <NftCard key={nft.id} {...nft} isSmall={isPhone} displayMode={displayMode} />
                          </PumpTokenContext.Provider>
                        ))
                      )}
                      {isFetchingNextPage && (
                        <>
                          {new Array(10)
                            .fill('')
                            ?.map((_, index) => (
                              <CardSkeleton key={index} isSmall={displayMode === DisplayMode.SMALL} />
                            ))}
                        </>
                      )}
                      <div ref={ref} style={{ gridColumn: '1 / -1' }}>
                        {isFetchingNextPage ? <p /> : hasNextPage ? <p /> : <p />}
                      </div>
                    </div>
                  )}
                </>
              ) : null}
              {isFetching &&
                !isFetchingNextPage &&
                !data?.pages?.[0]?.items?.length &&
                (displayMode === DisplayMode.LIST ? (
                  <div className={twMerge('w-full p-0')}>
                    <TableSkeleton
                      isPhone={isPhone}
                      columns={columns}
                      tableProps={{
                        radius: 'none',
                        shadow: 'none',
                        className: 'w-full p-0',
                        removeWrapper: true,
                        isCompact: true,
                      }}
                    />
                  </div>
                ) : (
                  <CardListSkeleton isSmall={displayMode === DisplayMode.SMALL} />
                ))}
              {!isFetching &&
                isError &&
                (data?.pages?.[0]?.items === null ||
                  data?.pages?.[0]?.items === undefined ||
                  data?.pages?.[0]?.items.length === 0) && (
                  <ErrorAndTryComp onAction={refetch} className="sm:h-[60vh]" />
                )}
              {!isFetching &&
                !isError &&
                (data?.pages?.[0]?.items === null ||
                  data?.pages?.[0]?.items === undefined ||
                  data?.pages?.[0]?.items.length === 0) && <EmptyComp className="sm:h-[60vh]" />}
            </div>
          </div>
        </div>
      </FilterContext.Provider>
    </GraphContext.Provider>
  )
})
Tokens.displayName = 'PumpTokens'
export default memo(Tokens)
