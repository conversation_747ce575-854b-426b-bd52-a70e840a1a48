import React, { AnchorHTMLAttributes, memo, ReactNode } from 'react'

import { twMerge } from 'tailwind-merge'

import Dropdown, { IDropdownItemProps } from '@/components/library/Dropdown'
import Input from '@/components/library/Input'

import { formatNumberWithZeroCount } from '@/libs/common/format'

export interface ICheckMethod {
  checked: boolean
  tips: string
}
interface ICheckInputProps extends Omit<AnchorHTMLAttributes<HTMLDivElement>, 'prefix'> {
  label: ReactNode
  prefix?: ReactNode
  suffix?: ReactNode
  disabled?: boolean
  inputClassName?: string
  defaultValue?: string
  isShowDropdown?: boolean
  placeholder?: string
  isUpterInput?: boolean
  dropDownList?: IDropdownItemProps[]
  checkResult?: ICheckMethod
  dropDownChange?: (value: string) => void
  changeValue?: (value: string) => void
  isUserInput?: boolean
  // checkMehtod?: (value: string) => ICheckMehod
}
const symbolDropdownList = [
  {
    key: 'WALG',
    value: 'WALG',
  },
]
const CheckNumberInput = (props: ICheckInputProps) => {
  const {
    label,
    prefix,
    suffix,
    defaultValue,
    inputClassName = '',
    className = '',
    disabled,
    placeholder,
    isShowDropdown = true,
    dropDownList = symbolDropdownList,
    checkResult,
    isUserInput = false,
    dropDownChange,
    changeValue,
  } = props

  const isCheckError = checkResult && !checkResult?.checked

  const changeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    changeValue?.(value)
  }

  const changeDropDown = (value: string | string[]) => {
    dropDownChange?.(value as string)
  }

  const SymbolDropdown = () => {
    if (!isShowDropdown) return null
    if (suffix) return suffix
    return (
      <Dropdown
        className="bg-transparent"
        targetClassName="font-medium text-quaternary"
        targetIconClassName="text-quaternary"
        list={dropDownList}
        onChange={changeDropDown}
      />
    )
  }

  return (
    <div className={twMerge('flex flex-col gap-1', className)}>
      <div className="text-sm">{label}</div>
      <Input
        disabled={disabled}
        placeholder={placeholder}
        prefix={prefix}
        value={isUserInput ? defaultValue : formatNumberWithZeroCount(defaultValue as string)}
        className={twMerge('py-0 pr-0 ', isCheckError && 'bg-buttonErrorHover', inputClassName)}
        inputClassName="text-start text-sm"
        suffix={<SymbolDropdown />}
        onChange={changeInput}
      />
      {isCheckError && <span className="text-sm font-normal text-error">{checkResult?.tips}</span>}
    </div>
  )
}

export default memo(CheckNumberInput)
