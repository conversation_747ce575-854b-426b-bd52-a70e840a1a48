import React, { memo, useEffect, useMemo, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { useAccount } from 'wagmi'

import CIQI404 from '@/components/Icon/CIQI404'
import CheckNumberInput, { ICheckMethod } from '@/components/Input/CheckNumberInput'
import Button from '@/components/library/Button'
import Input from '@/components/library/Input'

import { MAX_APPROVE } from '@/constant/global'
import useErc20Approve from '@/hooks/contract/fusion/useErc20Approve'
import useErc20BalanceOf from '@/hooks/contract/fusion/useErc20Balanceof'
import useTransferToken from '@/hooks/contract/fusion/useTransferFrom'
import useAllowance from '@/hooks/contract/token/useAllowance'
import useConnect from '@/hooks/useConnect'
import { isEVMChain } from '@/hooks/useIsEvm'
import { isEVMAddress } from '@/hooks/useSafeAddress'
import { useToast } from '@/hooks/useToast'
import { formatNumberWithZeroCount } from '@/libs/common/format'
import { checkNumberAndFormatZero, checkNumberAndMoreZero } from '@/libs/vaildata/number'
import { IAddress } from '@/types/IAddress'
import { useAppKitNetwork } from '@reown/appkit/react'

const TransferToken = () => {
  const { t } = useTranslate('token')
  const { address } = useAccount()
  const { chainId: currentChain } = useAppKitNetwork()
  const { errorToast, successToast } = useToast()
  const { withConnect } = useConnect()
  const [amount, setAmount] = useState<string>('')
  const [receiverAddress, setReceiverAddress] = useState<string>('')
  const [checkResult, setCheckResult] = useState<ICheckMethod>({ checked: true, tips: '' })
  const [isUserInput, setIsUserInput] = useState<boolean>(false)

  const { allowance, refetch: refetchAllowance } = useAllowance(address!, address!, 'fusion')
  const { balance: ciqiBalance, refetch } = useErc20BalanceOf(address!)
  const { erc20Approve, isLoading: isApproveLoading, isSuccess: isApproveSuccess } = useErc20Approve()
  const { transferToken, isError, isLoading, isSuccess, errorMessage } = useTransferToken()

  const isShowApprove = useMemo(() => allowance === 0 || allowance < Number(amount), [amount, allowance])

  const inputAmount = useMemo(() => {
    // const pattern = /^\d+(\.\d+)?$/
    // const isNumber = pattern.test(amount)
    // return isNumber ? formatToDecimal(amount).toString() : amount
    return amount
  }, [amount])

  const disableTransfer = useMemo(() => {
    return !checkResult.checked || !receiverAddress || !isEVMAddress(receiverAddress) || !isEVMChain(currentChain)
  }, [checkResult, receiverAddress, currentChain])

  useEffect(() => {
    if (isError) {
      errorToast(errorMessage)
      refetch()
    }
  }, [isError])

  useEffect(() => {
    if (isSuccess) {
      successToast(t('swap_transfer_token_success'))
      refetch()
    }
  }, [isSuccess])

  useEffect(() => {
    if (isApproveSuccess) {
      refetchAllowance()
    }
  }, [isApproveSuccess])

  const maxBalance = () => {
    setIsUserInput(false)
    setAmount(ciqiBalance.toString())
    checkAmount(ciqiBalance.toString())
  }

  const changeAmount = (value: string) => {
    setIsUserInput(true)
    const result = checkNumberAndFormatZero(value)
    if (!result) return
    setAmount(value)
    checkAmount(value)
  }

  const checkAmount = (value: string) => {
    const result = checkNumberAndMoreZero(value)
    if (Number(value) > ciqiBalance) {
      const result = {
        checked: false,
        tips: t('tips_balance_not_enough', { ns: 'common', token: 'CIQI' }),
      }
      setCheckResult(result)
      return
    }
    setCheckResult({
      checked: result.checked,
      tips: t(result.tips, { ns: 'common' }),
    })
  }

  const changeReceiverAddress = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setReceiverAddress(value)
  }

  const handleTransfer = withConnect(() =>
    transferToken(address as IAddress, receiverAddress as IAddress, inputAmount, 'token')
  )

  const handleApprove = withConnect(() => erc20Approve(address!, MAX_APPROVE as unknown as string))

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2.5 rounded-xl bg-buttonTertiary p-4">
        <div className="flex items-center gap-2">
          <CIQI404 />
          <span className="text-lg text-primary">CIQI404</span>
        </div>
        <span className=" text-xs text-quaternary5">
          {t('swap_transfer_token_balance', { balance: formatNumberWithZeroCount(ciqiBalance) })}
        </span>
      </div>
      <div>
        <span>{t('swap_transfer_token_amount')}</span>
        <CheckNumberInput
          label=""
          placeholder="0"
          changeValue={changeAmount}
          defaultValue={inputAmount}
          inputClassName="px-3 py-2.5"
          checkResult={checkResult}
          isUserInput={isUserInput}
          suffix={
            <span className=" cursor-pointer text-sm text-quaternary hover:text-primary" onClick={maxBalance}>
              {t('swap_transfer_token_max')}
            </span>
          }
        />
      </div>
      <div>
        <span>{t('swap_transfer_token_receiver')}</span>
        <Input
          placeholder="0x..."
          className="mt-0.5 rounded bg-buttonTertiary"
          inputClassName="text-sm text-primary font-medium text-start"
          defaultValue={receiverAddress}
          onChange={changeReceiverAddress}
        />
      </div>
      {isShowApprove ? (
        <Button size="lg" isLoading={isApproveLoading} disabled={!amount || disableTransfer} onClick={handleApprove}>
          {t('swap_transfer_token_approve')}
        </Button>
      ) : (
        <Button size="lg" isLoading={isLoading} disabled={disableTransfer} onClick={handleTransfer}>
          {t('swap_transfer_token_transfer')}
        </Button>
      )}
    </div>
  )
}

export default memo(TransferToken)
