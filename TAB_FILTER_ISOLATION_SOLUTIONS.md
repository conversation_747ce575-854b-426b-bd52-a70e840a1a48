# Tab筛选状态隔离解决方案

## 问题描述

多个tab共享同一个组件，但需要保持各自独立的筛选状态。当前问题：

1. 切换tab时筛选状态会被共享
2. URL参数清空时机与组件生命周期冲突
3. 不同tab的筛选条件相互影响

## 解决方案对比

### 方案一：URL参数命名空间（已实现）

**原理**: 为每个tab的筛选参数添加前缀，如 `trending_search`, `favorites_chainId`

**优点**:

- 支持浏览器前进/后退
- 可以通过URL分享特定tab的筛选状态
- 完整的状态持久化

**缺点**:

- URL会变得很长
- 实现复杂度较高
- 需要修改多个文件

**使用示例**:

```typescript
// PumpList.tsx
<Nfts tabPrefix={currentTab} homeTab={currentTab} />

// hooks中会自动处理
// trending tab: ?trending_search=bitcoin&trending_chainId=8453
// favorites tab: ?favorites_search=ethereum&favorites_chainId=1
```

### 方案二：组件强制重新挂载（推荐）

**原理**: 使用React的key属性，当tab切换时强制组件重新挂载

**优点**:

- 实现简单，代码改动最小
- 完全隔离各tab的状态
- 不会有状态共享问题

**缺点**:

- 切换tab时会丢失筛选状态
- 不支持浏览器前进/后退恢复筛选状态
- 每次切换都会重新加载数据

**实现方式**:

```typescript
// PumpList.tsx - 已经实现
<Nfts
  key={currentTab}  // 这个key确保tab切换时组件重新挂载
  homeTab={currentTab}
/>
```

### 方案三：状态提升 + Context

**原理**: 将筛选状态提升到父组件，使用Context管理各tab的独立状态

**优点**:

- 状态完全隔离
- 切换tab时保持筛选状态
- 可以实现跨tab的状态管理

**缺点**:

- 需要重构现有代码
- 增加代码复杂度
- 不支持URL状态同步

### 方案四：本地存储缓存

**原理**: 使用localStorage缓存每个tab的筛选状态

**优点**:

- 状态持久化
- 刷新页面后状态保持
- 实现相对简单

**缺点**:

- 不支持URL分享
- 需要处理存储清理
- 跨设备不同步

## 推荐方案选择

### 场景1: 简单隔离，不需要状态保持

**推荐**: 方案二（组件强制重新挂载）

- 代码改动最小
- 完全避免状态共享问题

### 场景2: 需要URL分享和浏览器导航支持

**推荐**: 方案一（URL参数命名空间）

- 完整的状态管理
- 支持所有浏览器功能

### 场景3: 需要状态保持但不需要URL支持

**推荐**: 方案三（状态提升）或方案四（本地存储）

## 最终实现方案

✅ **采用方案：条件读取URL参数**

### 核心原理

1. **组件重新挂载**: 使用 `key={currentTab}` 确保tab切换时组件重新挂载
2. **条件读取URL**: 只有当URL中的`type`参数与当前`homeTab`匹配时，才读取筛选参数
3. **清除URL参数**: 切换tab时清除所有筛选参数，只保留`type`参数

### 关键代码修改

**PumpList.tsx**:

```typescript
const handleTabChange = (key: React.Key) => {
  const newTab = key as HomeTabKey
  // 切换tab时，清除所有筛选参数，只保留type
  router.replace(`?type=${newTab}`)
  setCurrentTab(newTab)
}
```

**Nfts组件**:

```typescript
useEffect(() => {
  const searchParams = new URLSearchParams(window.location.search)
  const urlType = searchParams.get('type')

  // 只有当URL中的type与当前homeTab匹配时，才读取筛选参数
  if (!homeTab || urlType === homeTab) {
    // 读取并应用筛选参数
  }
}, [homeTab])
```

### 解决的问题

- ✅ 完全隔离各tab的筛选状态
- ✅ 避免URL参数在tab间共享
- ✅ 保持URL状态还原功能
- ✅ 支持浏览器前进/后退
- ✅ 代码改动最小

### 工作流程

1. 用户在trending tab设置筛选条件 → URL: `?type=trending&search=bitcoin`
2. 切换到favorites tab → URL: `?type=favorites` (筛选参数被清除)
3. favorites tab的Nfts组件检查URL中type=favorites，但没有筛选参数，所以使用默认状态
4. 切换回trending tab → URL: `?type=trending` (干净状态)
5. 如果用户直接访问 `?type=trending&search=bitcoin`，trending tab会正确恢复筛选状态
