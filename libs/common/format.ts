import { BN } from '@coral-xyz/anchor'
import dayjs from 'dayjs'
import { formatUnits } from 'viem'

import { supportChainId } from '../contract/wagmiConf'
import { chainInfo } from '@/constant/chainInfo'
import { IAddress } from '@/types/IAddress'
import { <PERSON><PERSON>hain } from '@/types/IChain'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)
/**
 * eg. 0x00A34dA88DDCd6b18094259CDc91c0730c955A8b -> 0x00A34...5A8b
 * @param address IAddress
 * @returns string
 */
export const formatAddress = (address: IAddress | string): string => {
  if (!address) return '0x'
  if (address === '0x') return '0x'
  const start = address.slice(0, 7)
  const end = address.slice(-4)
  return `${start}...${end}`
}

/**
 * eg. 0x53aE934bB7Da...250dB79993530
 * @param address IAddress
 * @returns string
 */
export const formatSwapAddress = (address: IAddress | string): string => {
  if (!address) return ''
  const start = address.slice(0, 14)
  const end = address.slice(-13)
  return `${start}...${end}`
}

/**
 * eg. 578...9979
 * @param tokenId string
 * @returns string
 */
export const formatTokenId = (tokenId: string) => {
  if (!tokenId) return ''
  const start = tokenId?.toString().slice(0, 3)
  const end = tokenId?.toString().slice(-4)
  return `${start}...${end}`
}

/**
 * eg. 0.05889->0.0588 ALG
 * @param price number | string
 * @param chainId IChain
 * @param isWrapper boolean
 * @param decimal number
 * @returns string
 */
export const formatToSymbol = (
  price: number | string,
  chainId: IChain = supportChainId,
  isWrapper = false,
  decimal = 4
) => {
  const numericPrice = typeof price === 'string' ? parseFloat(price || '0') : price
  let truncatedNum = Math.floor(numericPrice * 10000) / 10000
  truncatedNum = parseFloat(truncatedNum.toFixed(decimal))
  const currentChainInfo = chainInfo[chainId]
  const symbol = isWrapper ? currentChainInfo?.wrapperSymbol : currentChainInfo?.symbol

  return `${truncatedNum} ${symbol}`
}

/**
 * eg. 0.1111111->0.1111
 * @param price number | string
 * @param decimal number
 * @returns number
 */
export const formatToDecimal = (price: number | string, decimal = 5) => {
  const numericPrice = typeof price === 'string' ? parseFloat(price) : price
  let truncatedNum = Math.floor(numericPrice * 10000) / 10000
  truncatedNum = parseFloat(truncatedNum.toFixed(decimal))
  return truncatedNum
}

/**
 * 1720169041 -> '3d 4h 16m 11s'
 * @param time number | string
 * @returns string
 */
export const formatTimeToCountdown = (timestamp: number | string) => {
  const now = dayjs()
  const targetTime = dayjs(typeof timestamp === 'number' ? timestamp * 1000 : timestamp)
  const diff = targetTime.diff(now)
  const isExpired = diff < 0

  const duration = dayjs.duration(diff)
  const days = isExpired ? 0 : Math.floor(duration.asDays())
  const hours = isExpired ? 0 : duration.hours()
  const minutes = isExpired ? 0 : duration.minutes()
  const seconds = isExpired ? 0 : duration.seconds()

  return {
    days,
    hours,
    minutes,
    seconds,
    format: `${days}d ${hours}h ${minutes}m ${seconds}s`,
  }
}

/**
 * 2024-01-01 -> 1640995200
 * @param timestamp number| string
 * @returns number
 */
export const formatTimeStamp = (time?: number | string | Date) => {
  const timeTemp = time || Date.now()
  return dayjs(timeTemp).unix()
}
/**
 *
 * @param count
 * @returns
 */
export function formatCount(count: number, space?: boolean): string {
  const separator = space ? '' : ''

  if (count >= 1_000_000) {
    const formattedCount = (count / 1_000_000).toFixed(1)
    return formattedCount.endsWith('.0')
      ? formattedCount.slice(0, -2) + `${separator}M`
      : formattedCount + `${separator}M`
  } else if (count >= 1_000) {
    const formattedCount = (count / 1_000).toFixed(1)
    return formattedCount.endsWith('.0')
      ? formattedCount.slice(0, -2) + `${separator}K`
      : formattedCount + `${separator}K`
  }

  return count.toString()
}

export const getChangeChainId = (chainId: number | string) => {
  switch (Number(chainId)) {
    case 1:
      return 'ethereum'
    case 137:
      return 'matic'
    default:
      return 'ethereum'
  }
}
export function formatNumber(num: number, decimal = 5) {
  const numStr = num.toString()
  if (numStr.includes('.') && numStr.includes('e') === false) {
    const [integerPart, decimalPart] = numStr.split('.')
    if (decimalPart.length > decimal) {
      return `${integerPart}.${decimalPart.slice(0, decimal)}`
    }
    return numStr
  }
  return numStr
}
/**
 * format date seconds to date string
 * @param seconds number
 * @returns
 */
export function formatTimestampForSeconds(seconds: number) {
  return dayjs.unix(seconds).format('M/D/YYYY, h:mm A')
}

export const formatNumberFour = (num: number) => {
  let formattedNum: any = num.toFixed(4)

  formattedNum = parseFloat(formattedNum)

  return formattedNum
}

export const formatWithCommas = (num: any) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const formatWithCommas2 = (num: number | string, toFixed: boolean = false) => {
  if (toFixed) {
    let numStr = '' + num
    if (numStr.includes('.')) {
      numStr = Number(num).toFixed(2)
    }
    return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}
interface IPrecisionInfoMap {
  [index: string]: number
}
export const precisionInfo: IPrecisionInfoMap = {
  CNY: 2,
  MATIC: 5,
  ETH: 5,
  BNB: 5,
  SOL: 5,
  ALG: 5,
  WETH: 5,
  WALG: 5,
  $: 2,
}
export const decimalInfo: IPrecisionInfoMap = {
  CNY: 0.01,
  MATIC: 0.00001,
  ETH: 0.00001,
  BNB: 0.00001,
  ALG: 0.00001,
  SOL: 0.00001,
  WETH: 0.00001,
  WALG: 0.00001,
  $: 0.01,
}
export const truncateDecimals = (num: number | string, digits = 2) => {
  const numStr = num.toString()
  const decimalIndex = numStr.indexOf('.')
  if (decimalIndex !== -1) {
    const endPos = decimalIndex + digits + 1
    const truncatedStr = numStr.substring(0, endPos > numStr.length ? numStr.length : endPos)
    return parseFloat(truncatedStr)
  }
  return num
}
export const formatNumber2 = (number: number | string, type: 'separator' | 'symbol' | 'default', currency: string) => {
  let num = number
  if (typeof num === 'string') {
    num = Number(num)
  }
  if (isNaN(num)) {
    return 0
  }
  if (num === 0) {
    return 0
  }
  // num = Number(num.toFixed(precisionInfo[currency]))
  num = Number(truncateDecimals(num, precisionInfo[currency]))
  const compNum = decimalInfo[currency]
  if (num < compNum) {
    return `< ${compNum}`
  }
  if (num < 1000) return num
  if (type === 'default') {
    return num
  }
  if (type === 'separator') {
    return num.toLocaleString()
  }
  if (type === 'symbol') {
    return Math.round(num / 10) / 100 + 'K'
  }
}
export const formatPercentage = (number: number | string) => {
  const point = Number(number)
  if (point === 0 || isNaN(point)) {
    return '0%'
  }
  let str = (Math.trunc(point * 100) / 100).toString()
  // var str = Number(point * 100).toFixed(2);
  str += '%'
  return str
}

export function formatDateTime(input: string | number | Date) {
  const date = new Date(input)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}
export function formatTimeStampToDay(timestamp: string) {
  return dayjs(Number(timestamp) * 1000).format('MM/DD/YYYY')
}
const toUnicodeSubscript = (count: number): string => {
  const subscriptDigits = '₀₁₂₃₄₅₆₇₈₉'
  return count
    .toString()
    .split('')
    .map((digit) => subscriptDigits[parseInt(digit, 10)])
    .join('')
}

export const formatNumberWithZeroCount = (number: number | string) => {
  let inputValue = typeof number === 'number' ? number.toString() : number
  if (!inputValue) return inputValue
  const decimalParts = inputValue.split('.')
  if (decimalParts.length > 1) {
    const decimalPart = decimalParts[1]
    const significantDigits = decimalPart.replace(/^0+/, '') // Remove leading zeros
    if (Number(inputValue) >= 1) {
      // 大于等于1的数字：保留小数点后5位，然后去掉尾部无意义的0
      const truncated = decimalPart.slice(0, 5)
      const cleaned = truncated.replace(/0+$/, '') // 去掉尾部的0
      inputValue = cleaned ? `${decimalParts[0]}.${cleaned}` : decimalParts[0]
    } else {
      if (decimalPart.length > 18) {
        inputValue = `${decimalParts[0]}.${decimalPart.slice(0, 18)}` // Truncate to 18 characters
      } else if (significantDigits.length > 5) {
        // 小于1的数字：如果去掉前导零后的数字超过5位，保留5位有效数字，然后去掉尾部无意义的0
        const leadingZeros = decimalPart.match(/^0+/)?.[0]?.length || 0
        const truncateLength = leadingZeros + 5 // Keep leading zeros and 5 significant digits
        const truncated = decimalPart.slice(0, truncateLength)
        const cleaned = truncated.replace(/0+$/, '') // 去掉尾部的0
        inputValue = `${decimalParts[0]}.${cleaned}`
      } else {
        // 不超过5位有效数字的情况，去掉尾部无意义的0
        const cleaned = decimalPart.replace(/0+$/, '')
        inputValue = cleaned ? `${decimalParts[0]}.${cleaned}` : decimalParts[0]
      }
    }
  }
  return inputValue
}

export const formatNumberWithUnicodeSubscript = (number: number | string, currency: string = '$'): string => {
  const numStr = typeof number === 'number' ? number.toString() : number

  if (isNaN(Number(numStr)) || numStr === '0') {
    return '0'
  }

  // Handle scientific notation separately
  const isScientificNotation = numStr.includes('e')
  if (isScientificNotation) {
    const [base, exponent] = numStr.split('e').map(Number)
    console.log('base', base)
    if (exponent > 0) {
      return numStr
    }
  }
  const normalizedNumStr = isScientificNotation
    ? currency === 'SOL'
      ? Number(numStr).toFixed(9)
      : Number(numStr).toFixed(21)
    : numStr

  const precision = precisionInfo[currency] || 2

  const [integerPart, decimalPart = ''] = normalizedNumStr.split('.')

  if (!decimalPart) {
    return integerPart
  }

  const match = decimalPart.match(/^(0+)/)
  const zeroCount = match ? match[1].length : 0

  const significantPart = decimalPart.slice(zeroCount, zeroCount + precision).replace(/0+$/, '')

  if (zeroCount >= 2) {
    return `${integerPart}.0${toUnicodeSubscript(zeroCount)}${significantPart}`
  } else {
    return `${integerPart}.${decimalPart.slice(0, precision)}`
  }
}
export const formatPrice = (number: number | string, currency?: string, type: 'symbol' | 'default' = 'default') => {
  let num = number
  if (typeof num === 'string') {
    num = Number(num)
  }
  if (isNaN(num)) {
    return type === 'symbol' ? `0 ${currency}` : 0
  }
  if (num === 0) {
    return type === 'symbol' ? `0 ${currency}` : 0
  }
  num = formatNumberWithUnicodeSubscript(number, currency)
  return type === 'symbol' ? `${num} ${currency}` : num
}
export function formatNumberWithCommas(num: string) {
  const [integer, decimal] = num.toString().split('.')
  const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  return decimal ? `${formattedInteger}.${decimal}` : formattedInteger
}
export function replaceSpecialCharactersWithSpace(input: string) {
  return input.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_]/g, ' ')
}
export function sanitizeForJSON(input: string) {
  return input.replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
}
export const abbreviateNumber = (num: number | string): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num

  const units = [
    { value: 1e15, symbol: 'Q' }, // Quadrillion (10^15)
    { value: 1e12, symbol: 'T' }, // Trillion (10^12)
    { value: 1e9, symbol: 'B' }, // Billion (10^9)
    { value: 1e6, symbol: 'M' }, // Million (10^6)
    { value: 1e3, symbol: 'K' }, // Thousand (10^3)
  ]

  for (const unit of units) {
    if (Math.abs(number) >= unit.value) {
      if (Math.abs(number) >= 1e20) {
        return '> 1e20'
      }
      // Format the number to 2 decimal places and remove trailing .00 if any
      return (number / unit.value).toFixed(2).replace(/\.00$/, '') + unit.symbol
    }
  }
  return number.toString()
}
export const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date
    .toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
    .replace(',', '')
}
export function getPastTimestamps() {
  const now = Math.floor(Date.now() / 1000)

  return {
    fiveMinutesAgo: now - 5 * 60,
    oneHourAgo: now - 60 * 60,
    sixHoursAgo: now - 6 * 60 * 60,
    twentyFourHoursAgo: now - 24 * 60 * 60,
  }
}

export const formatTimeSince = (dateString: string) => {
  const now = dayjs()
  const date = dayjs(dateString)
  const diffMinutes = now.diff(date, 'minute')
  const diffHours = now.diff(date, 'hour')
  const diffDays = now.diff(date, 'day')

  if (diffDays > 0) {
    return `${diffDays}d`
  }
  if (diffHours > 0) {
    return `${diffHours}h`
  }
  return `${diffMinutes}m`
}
export function formatToLamports(amount: string | number): BN {
  const amountStr = typeof amount === 'number' ? amount.toString() : amount

  const [integerPart, decimalPart = ''] = amountStr.split('.')

  const paddedDecimal = (decimalPart + '000000000').slice(0, 9)

  const lamportsStr = `${integerPart}${paddedDecimal}`.replace(/^0+/, '') || '0'

  return new BN(lamportsStr)
}

export const formatBn = (amount: any) => {
  return Number(formatUnits(amount, 9))
}
